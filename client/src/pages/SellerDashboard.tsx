import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Plus, Edit, Trash2, ImageIcon } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '../context/AuthContext';
import { addBook, updateBook, deleteBook, getBooksBySeller } from '../services/bookService';
import { getAllOrders } from '../services/orderService';
import { convertImageFileToBase64, validateImageSize } from '../lib/image';
import { Book } from '@shared/schema';
import { useToast } from '@/hooks/use-toast';

const bookSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  author: z.string().min(1, 'Author is required'),
  price: z.number().positive('Price must be positive'),
  stock: z.number().int().min(0, 'Stock must be non-negative'),
});

type BookFormData = z.infer<typeof bookSchema>;

interface SellerDashboardProps {
  activeView?: string;
}

export function SellerDashboard({ activeView = 'dashboard' }: SellerDashboardProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [books, setBooks] = useState<Book[]>([]);
  const [editingBook, setEditingBook] = useState<Book | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');
  const [selectedImage, setSelectedImage] = useState<string>('');
  const [stats, setStats] = useState({
    totalBooks: 0,
    booksSold: 0,
    revenue: 0,
  });

  const form = useForm<BookFormData>({
    resolver: zodResolver(bookSchema),
    defaultValues: { title: '', author: '', price: 0, stock: 0 },
  });

  useEffect(() => {
    if (user) {
      loadBooks();
      loadStats();
    }
  }, [user]);

  const loadBooks = () => {
    if (user) {
      const sellerBooks = getBooksBySeller(user.id);
      setBooks(sellerBooks);
    }
  };

  const loadStats = () => {
    if (!user) return;
    
    const sellerBooks = getBooksBySeller(user.id);
    const orders = getAllOrders();
    
    let booksSold = 0;
    let revenue = 0;

    orders.forEach(order => {
      order.items.forEach(item => {
        const book = sellerBooks.find(b => b.id === item.bookId);
        if (book) {
          booksSold += item.qty;
          revenue += item.priceAtPurchase * item.qty;
        }
      });
    });

    setStats({
      totalBooks: sellerBooks.length,
      booksSold,
      revenue,
    });
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!validateImageSize(file, 5)) {
      toast({
        title: "Image too large",
        description: "Please select an image smaller than 5MB",
        variant: "destructive",
      });
      return;
    }

    try {
      const base64 = await convertImageFileToBase64(file);
      setSelectedImage(base64);
      setImagePreview(base64);
    } catch (error) {
      toast({
        title: "Image upload failed",
        description: "Unable to process the selected image",
        variant: "destructive",
      });
    }
  };

  const handleSubmit = async (data: BookFormData) => {
    if (!user) return;

    try {
      if (editingBook) {
        const updatedBook = updateBook(editingBook.id, {
          ...data,
          imageBase64: selectedImage || editingBook.imageBase64,
        });
        
        if (updatedBook) {
          toast({
            title: "Book updated successfully",
          });
        }
      } else {
        addBook({
          ...data,
          sellerId: user.id,
          imageBase64: selectedImage,
        });
        
        toast({
          title: "Book added successfully",
        });
      }

      form.reset();
      setEditingBook(null);
      setImagePreview('');
      setSelectedImage('');
      loadBooks();
      loadStats();
    } catch (error) {
      toast({
        title: "Operation failed",
        description: error instanceof Error ? error.message : "Unable to save book",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (book: Book) => {
    setEditingBook(book);
    form.reset({
      title: book.title,
      author: book.author,
      price: book.price,
      stock: book.stock,
    });
    setImagePreview(book.imageBase64 || '');
    setSelectedImage(book.imageBase64 || '');
  };

  const handleDelete = (bookId: string) => {
    if (window.confirm('Are you sure you want to delete this book?')) {
      const success = deleteBook(bookId);
      if (success) {
        toast({
          title: "Book deleted successfully",
        });
        loadBooks();
        loadStats();
      }
    }
  };

  const cancelEdit = () => {
    setEditingBook(null);
    form.reset();
    setImagePreview('');
    setSelectedImage('');
  };

  const renderContent = () => {
    switch (activeView) {
      case 'add-book':
        return renderAddBookForm();
      case 'my-books':
        return renderMyBooksTable();
      case 'my-orders':
        return renderSalesView();
      default:
        return renderDashboardView();
    }
  };

  const renderDashboardView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      {renderAddBookForm()}
      {renderStatsCard()}
    </div>
  );

  const renderAddBookForm = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Plus className="h-5 w-5" />
          {editingBook ? 'Edit Book' : 'Add New Book'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4" data-testid="form-book">
          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              {...form.register('title')}
              data-testid="input-book-title"
            />
            {form.formState.errors.title && (
              <p className="text-sm text-destructive">{form.formState.errors.title.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="author">Author</Label>
            <Input
              id="author"
              {...form.register('author')}
              data-testid="input-book-author"
            />
            {form.formState.errors.author && (
              <p className="text-sm text-destructive">{form.formState.errors.author.message}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="price">Price</Label>
              <Input
                id="price"
                type="number"
                step="0.01"
                {...form.register('price', { valueAsNumber: true })}
                data-testid="input-book-price"
              />
              {form.formState.errors.price && (
                <p className="text-sm text-destructive">{form.formState.errors.price.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="stock">Stock</Label>
              <Input
                id="stock"
                type="number"
                {...form.register('stock', { valueAsNumber: true })}
                data-testid="input-book-stock"
              />
              {form.formState.errors.stock && (
                <p className="text-sm text-destructive">{form.formState.errors.stock.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="image">Book Cover Image</Label>
            <Input
              id="image"
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              data-testid="input-book-image"
            />
            {imagePreview && (
              <div className="mt-2 p-2 border-2 border-dashed border-border rounded-lg text-center">
                <img
                  src={imagePreview}
                  alt="Book cover preview"
                  className="max-w-full h-32 mx-auto object-cover rounded"
                  data-testid="img-book-preview"
                />
              </div>
            )}
          </div>

          <div className="flex gap-2">
            <Button type="submit" className="flex-1" data-testid="button-save-book">
              {editingBook ? 'Update Book' : 'Add Book'}
            </Button>
            {editingBook && (
              <Button type="button" variant="outline" onClick={cancelEdit} data-testid="button-cancel-edit">
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );

  const renderStatsCard = () => (
    <Card>
      <CardHeader>
        <CardTitle>Your Statistics</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex justify-between">
          <span className="text-muted-foreground">Total Books:</span>
          <span className="font-semibold" data-testid="text-total-books">{stats.totalBooks}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-muted-foreground">Books Sold:</span>
          <span className="font-semibold" data-testid="text-books-sold">{stats.booksSold}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-muted-foreground">Total Revenue:</span>
          <span className="font-semibold text-green-600" data-testid="text-revenue">
            ${stats.revenue.toFixed(2)}
          </span>
        </div>
      </CardContent>
    </Card>
  );

  const renderMyBooksTable = () => (
    <Card>
      <CardHeader>
        <CardTitle>My Books</CardTitle>
      </CardHeader>
      <CardContent>
        {books.length === 0 ? (
          <div className="text-center text-muted-foreground py-8" data-testid="text-no-books">
            You haven't added any books yet.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Cover</TableHead>
                  <TableHead>Title</TableHead>
                  <TableHead>Author</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Stock</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {books.map((book) => (
                  <TableRow key={book.id} data-testid={`row-book-${book.id}`}>
                    <TableCell>
                      <img
                        src={book.imageBase64 || `https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&h=60&q=80`}
                        alt={`${book.title} cover`}
                        className="w-8 h-12 object-cover rounded"
                        data-testid={`img-book-cover-${book.id}`}
                      />
                    </TableCell>
                    <TableCell className="font-medium" data-testid={`text-book-title-${book.id}`}>
                      {book.title}
                    </TableCell>
                    <TableCell data-testid={`text-book-author-${book.id}`}>{book.author}</TableCell>
                    <TableCell data-testid={`text-book-price-${book.id}`}>${book.price.toFixed(2)}</TableCell>
                    <TableCell>
                      <Badge variant={book.stock > 0 ? "secondary" : "destructive"} data-testid={`text-book-stock-${book.id}`}>
                        {book.stock}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(book)}
                          data-testid={`button-edit-book-${book.id}`}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDelete(book.id)}
                          data-testid={`button-delete-book-${book.id}`}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );

  const renderSalesView = () => (
    <Card>
      <CardHeader>
        <CardTitle>My Sales</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-muted rounded-lg">
              <p className="text-2xl font-bold text-primary">{stats.totalBooks}</p>
              <p className="text-sm text-muted-foreground">Total Books</p>
            </div>
            <div className="text-center p-4 bg-muted rounded-lg">
              <p className="text-2xl font-bold text-green-600">{stats.booksSold}</p>
              <p className="text-sm text-muted-foreground">Books Sold</p>
            </div>
            <div className="text-center p-4 bg-muted rounded-lg">
              <p className="text-2xl font-bold text-green-600">${stats.revenue.toFixed(2)}</p>
              <p className="text-sm text-muted-foreground">Total Revenue</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="p-6">
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-foreground mb-2">
          {activeView === 'add-book' ? 'Add New Book' :
           activeView === 'my-books' ? 'My Books' :
           activeView === 'my-orders' ? 'My Sales' : 'Seller Dashboard'}
        </h2>
        <p className="text-muted-foreground">
          {activeView === 'add-book' ? 'Add a new book to your inventory' :
           activeView === 'my-books' ? 'Manage your book listings' :
           activeView === 'my-orders' ? 'View your sales performance' : 'Manage your book inventory'}
        </p>
      </div>

      {renderContent()}
    </div>
  );
}
