import { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { Header } from '../components/Header';
import { Sidebar } from '../components/Sidebar';
import { CriteriaTracker } from '../components/CriteriaTracker';
import { BuyerDashboard } from './BuyerDashboard';
import { SellerDashboard } from './SellerDashboard';
import { AdminDashboard } from './AdminDashboard';

export function Dashboard() {
  const { user } = useAuth();
  const [activeView, setActiveView] = useState('dashboard');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  if (!user) return null;

  const renderContent = () => {
    if (user.role === 'admin') {
      return <AdminDashboard activeView={activeView} />;
    } else if (user.role === 'seller') {
      return <SellerDashboard activeView={activeView} />;
    } else {
      return <BuyerDashboard activeView={activeView} />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header
        onMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        isMobileMenuOpen={isMobileMenuOpen}
      />
      
      <div className="flex">
        <Sidebar
          activeView={activeView}
          onViewChange={setActiveView}
          isMobile={false}
        />
        
        <Sidebar
          activeView={activeView}
          onViewChange={setActiveView}
          isMobile={true}
          isOpen={isMobileMenuOpen}
          onClose={() => setIsMobileMenuOpen(false)}
        />

        <main className="flex-1 overflow-x-hidden overflow-y-auto">
          {renderContent()}
        </main>
      </div>

      <CriteriaTracker />

      {/* Toast Container */}
      <div id="toast-container" className="toast-container"></div>
    </div>
  );
}
