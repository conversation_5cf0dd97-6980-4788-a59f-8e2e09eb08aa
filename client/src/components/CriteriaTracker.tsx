import { useEffect, useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckSquare } from 'lucide-react';
import { getCriteriaProgress } from '../lib/criteria';
import { CriteriaFlags } from '@shared/schema';

export function CriteriaTracker() {
  const [progress, setProgress] = useState({ completed: 0, total: 15, flags: {} as CriteriaFlags });

  useEffect(() => {
    const updateProgress = () => {
      setProgress(getCriteriaProgress());
    };

    // Update immediately
    updateProgress();

    // Set up interval to check for updates
    const interval = setInterval(updateProgress, 1000);

    return () => clearInterval(interval);
  }, []);

  const criteriaNumbers = Array.from({ length: 15 }, (_, i) => i + 1);

  return (
    <Card className="fixed bottom-4 left-4 w-80 z-40" data-testid="criteria-tracker">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center">
          <CheckSquare className="mr-2 h-4 w-4 text-primary" />
          Criteria Progress
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-5 gap-1 mb-3">
          {criteriaNumbers.map((num) => {
            const criterionKey = `criterion${num}` as keyof CriteriaFlags;
            const isCompleted = progress.flags[criterionKey];
            
            return (
              <Badge
                key={num}
                variant={isCompleted ? "default" : "secondary"}
                className={`w-8 h-8 rounded flex items-center justify-center text-xs font-medium ${
                  isCompleted 
                    ? 'bg-green-100 text-green-800 border-green-200' 
                    : 'bg-muted text-muted-foreground border-border'
                }`}
                data-testid={`criteria-chip-${num}`}
              >
                {num}
              </Badge>
            );
          })}
        </div>
        <div className="text-xs text-muted-foreground" data-testid="criteria-progress-text">
          <span className="font-medium">{progress.completed}</span>/{progress.total} Completed
        </div>
      </CardContent>
    </Card>
  );
}
