import { Home, Book, Users, ShoppingCart, BarChart3, Plus, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useAuth } from '../context/AuthContext';

interface SidebarProps {
  activeView: string;
  onViewChange: (view: string) => void;
  isMobile?: boolean;
  isOpen?: boolean;
  onClose?: () => void;
}

export function Sidebar({ activeView, onViewChange, isMobile = false, isOpen = true, onClose }: SidebarProps) {
  const { user } = useAuth();

  if (!user) return null;

  const getMenuItems = () => {
    const baseItems = [
      { id: 'dashboard', label: 'Dashboard', icon: Home },
    ];

    if (user.role === 'admin') {
      return [
        ...baseItems,
        { id: 'books', label: 'All Books', icon: Book },
        { id: 'users', label: 'Users', icon: Users },
        { id: 'orders', label: 'Orders', icon: ShoppingCart },
        { id: 'analytics', label: 'Analytics', icon: BarChart3 },
      ];
    } else if (user.role === 'seller') {
      return [
        ...baseItems,
        { id: 'add-book', label: 'Add Book', icon: Plus },
        { id: 'my-books', label: 'My Books', icon: Book },
        { id: 'my-orders', label: 'My Sales', icon: ShoppingCart },
      ];
    } else {
      return [
        ...baseItems,
        { id: 'books', label: 'Browse Books', icon: Book },
        { id: 'my-orders', label: 'My Orders', icon: ShoppingCart },
        { id: 'profile', label: 'Profile', icon: User },
      ];
    }
  };

  const menuItems = getMenuItems();

  const handleItemClick = (viewId: string) => {
    onViewChange(viewId);
    if (isMobile && onClose) {
      onClose();
    }
  };

  return (
    <aside
      className={cn(
        "bg-card border-r border-border",
        isMobile
          ? `fixed inset-y-0 left-0 z-50 w-64 transform transition-transform ${
              isOpen ? 'translate-x-0' : '-translate-x-full'
            }`
          : "w-64 hidden md:block"
      )}
    >
      <div className="h-full px-3 py-4">
        {isMobile && (
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Menu</h2>
            <Button variant="ghost" size="icon" onClick={onClose} data-testid="button-close-mobile-menu">
              <span className="sr-only">Close menu</span>
              ×
            </Button>
          </div>
        )}
        
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            return (
              <li key={item.id}>
                <Button
                  variant={activeView === item.id ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start",
                    activeView === item.id && "bg-accent text-accent-foreground"
                  )}
                  onClick={() => handleItemClick(item.id)}
                  data-testid={`button-nav-${item.id}`}
                >
                  <Icon className="mr-2 h-4 w-4" />
                  {item.label}
                </Button>
              </li>
            );
          })}
        </ul>
      </div>
    </aside>
  );
}
